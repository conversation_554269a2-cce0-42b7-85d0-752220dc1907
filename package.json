{"name": "zimbora-web", "version": "0.8.0", "private": true, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "scripts": {"ngrok": "ngrok http 3000", "ts:lint": "tsc", "email": "ts-node scripts/email-envs.ts", "dev": "cross-env NODE_OPTIONS='--inspect' next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "npx prisma generate", "test": "vitest run", "sanity:seed": "node --experimental-strip-types ./sanity/seed-ndjson.ts && sanity dataset import seed.ndjson dev", "sanity:migrate-categories": "npx tsx --env-file=.env sanity/migrations/convert-category-names-to-internationalized.ts", "sanity:migrate-events": "npx tsx --env-file=.env sanity/migrations/convert-event-names-descriptions-to-internationalized.ts"}, "dependencies": {"@clerk/nextjs": "^6.25.4", "@headlessui/react": "^1.7.13", "@heroicons/react": "^2.0.16", "@prisma/client": "^6.4.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@sanity/document-internationalization": "^3.3.3", "@sanity/icons": "^3.7.4", "@sanity/image-url": "1", "@sanity/vision": "^4.1.1", "@workattackdev/wdk": "^0.4.5", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "expo-server-sdk": "^3.15.0", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.525.0", "next": "15.4.3", "next-sanity": "^10.0.3", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-query": "^3.39.3", "sanity": "^4.1.1", "sanity-plugin-internationalized-array": "^3.1.4", "sonner": "^2.0.6", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5", "zustand": "5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@faker-js/faker": "^9.5.0", "@ngrok/ngrok": "^1.5.1", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/postcss": "^4.1.11", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.1", "@types/multer": "^1.4.7", "@types/node": "22.15.1", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "autoprefixer": "^10.4.21", "bcrypt": "^5.1.0", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "eslint": "9.31.0", "eslint-config-next": "15.4.3", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.4.1", "sass": "^1.58.3", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "5.8.3", "vitest": "^3.2.4"}, "pnpm": {"onlyBuiltDependencies": ["@clerk/shared", "@parcel/watcher", "@prisma/client", "@prisma/engines", "@tailwindcss/oxide", "bcrypt", "core-js", "esbuild", "prisma", "sharp", "unrs-resolver"], "overrides": {"@types/react": "19.0.10", "@types/react-dom": "19.0.4"}}}