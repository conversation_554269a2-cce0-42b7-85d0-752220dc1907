import { Metadata } from "next";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { getEventById } from "@/app/(site)/event/controller";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { buttonStyles } from "@/features/client/core/components/Button";
import { cache } from "react";
import { APP_CONFIG, generateDeepLink } from "@/lib/app-config";
import {
  getLanguageFromHeaders,
  LocaleType,
  resolveLanguage,
} from "../../api/i18n";
import {
  getFormattedLocation,
  hasLocationMapLink,
  getLocationMapLink,
} from "@/lib/utils/location";

type Props = {
  params: Promise<{ id: string }>;
};

const cachedGetEventById = cache(async (id: string, lang?: LocaleType) => {
  const headersList = await headers();
  const detectedLang = lang || getLanguageFromHeaders(headersList);
  return getEventById(id, detectedLang);
});

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const id = (await params).id;
  const lang = resolveLanguage({ headers: await headers() });
  const event = await cachedGetEventById(id, lang);

  if (!event) {
    return {
      title: "Evento não encontrado",
      description: "O evento não foi encontrado",
    };
  }

  return {
    title: event.name,
    description: event.description,
    openGraph: {
      title: event.name,
      description: event.description,
      url: `https://zimbora.ao/events/${event._id}`,
      siteName: "Zimbora",
      locale: "pt_AO",
      type: "website",
      images: event.medias?.[0]?.url
        ? [
            {
              url: event.medias[0].url,
            },
          ]
        : undefined,
    },
    twitter: {
      card: "summary",
      site: "@zimbora",
      title: event.name,
      description: event.description,
      images: event.medias?.[0]?.url,
    },
  };
}

export default async function EventPage({ params }: Props) {
  const { id } = await params;
  const lang = resolveLanguage({ headers: await headers() });
  const event = await cachedGetEventById(id, lang);
  if (!event) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        Evento não encontrado
      </div>
    );
  }

  const headersList = await headers();
  const userAgent = headersList.get("user-agent") || "";
  const isMobile = /iPad|iPhone|iPod|Android/i.test(userAgent);

  if (isMobile) {
    const deepLink = generateDeepLink(`events/${id}`);
    redirect(deepLink);
  }

  return (
    <main className="mt-9 flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
      <div className="flex w-full max-w-4xl flex-col overflow-hidden rounded-lg bg-white shadow-lg md:flex-row">
        {event.medias?.[0]?.url && (
          <div className="relative w-full overflow-hidden rounded-l-lg md:w-1/3">
            {" "}
            <Image
              fill
              src={event.medias[0].url}
              alt={event.name}
              className="object-cover"
            />
          </div>
        )}
        <div
          className={`p-6 ${event.medias?.[0]?.url ? "w-full md:w-2/3" : "w-full"}`}
        >
          <h1 className="mb-2 text-3xl font-bold text-[--primary1]">
            {event.name}
          </h1>
          <p className="mb-4 text-gray-600">{event.description}</p>
          <div className="text-muted-foreground space-y-1.5 text-sm">
            <p>
              <strong className="text-foreground font-medium">Local:</strong>{" "}
              {hasLocationMapLink(event.location) ? (
                <a
                  href={getLocationMapLink(event.location)!}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  {getFormattedLocation(event.location)}
                </a>
              ) : (
                getFormattedLocation(event.location)
              )}
            </p>
            <p>
              <strong className="text-foreground font-medium">
                Organizador:
              </strong>{" "}
              {event.organizer}
            </p>
            <p>
              <strong className="text-foreground font-medium">Data:</strong>{" "}
              {new Date(event.startAt).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>
      <div className="mt-8 flex flex-wrap justify-center gap-4">
        <a
          href={generateDeepLink(`events/${id}`)}
          className={cn(buttonStyles.variants.primary)}
        >
          Abrir na App
        </a>
        <a
          href={APP_CONFIG.appStore.url}
          target="_blank"
          rel="noopener noreferrer"
          className={cn(buttonStyles.variants.secondary)}
        >
          Baixar na App Store
        </a>
        <a
          href={APP_CONFIG.googlePlay.url}
          target="_blank"
          rel="noopener noreferrer"
          className={cn(buttonStyles.variants.secondary)}
        >
          Baixar na Google Play
        </a>
      </div>
    </main>
  );
}
