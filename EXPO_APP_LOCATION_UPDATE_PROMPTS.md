# Expo App Location Schema Update Prompts

These prompts will help you update your Expo app to work with the new event location object structure.

## 1. Update TypeScript Types

**Prompt:**
```
Update my Expo app's TypeScript types to handle the new event location schema. The location field has changed from a string to an object with this structure:

```typescript
type EventLocation = {
  name: string;           // The specific location name
  province: string;       // Angola province (luanda, benguela, etc.)
  mapLink?: string | null; // Optional Google Maps URL
}
```

The app should support backward compatibility with events that still have string locations. Create a union type and utility functions to handle both formats safely.

Please update:
1. Event type definitions
2. Location utility functions for display
3. Type guards to check location format
4. Helper functions to extract location data consistently

Make sure the types work with both old string format and new object format for seamless migration.
```

## 2. Update API Response Handling

**Prompt:**
```
Update my Expo app's API response handling for the new event location schema. The API now returns location as an object instead of a string:

```typescript
// Old format
location: "Centro de Convenções de Talatona"

// New format  
location: {
  name: "Centro de Convenções de Talatona",
  province: "luanda",
  mapLink: "https://maps.google.com/..."
}
```

Please update:
1. API response parsing/validation
2. Data transformation functions
3. Error handling for malformed location data
4. Caching logic if location data structure affects cache keys
5. Any API client code that processes event location data

Ensure backward compatibility with events that might still return string locations during the migration period.
```

## 3. Update UI Components - Event Display

**Prompt:**
```
Update my Expo app's event display components to show the new location object format. The location field now contains:

- `name`: Specific location name (e.g., "Centro de Convenções de Talatona")  
- `province`: Angola province (e.g., "luanda", "benguela")
- `mapLink`: Optional Google Maps URL

Please update the UI components to:

1. **Display formatted location**: Show "Location Name, Province" format
2. **Handle map links**: Make location clickable if mapLink exists, opening in maps app
3. **Backward compatibility**: Handle events with old string location format
4. **Loading states**: Show appropriate loading/error states
5. **Accessibility**: Ensure proper accessibility labels for location info

For map integration:
- Use `Linking.openURL()` for map links
- Show a map icon when map link is available
- Handle cases where map link is null/undefined

Create reusable location display components that work across different screens (event list, event details, etc.).
```

## 4. Update Search and Filtering

**Prompt:**
```
Update my Expo app's search and filtering functionality for the new event location schema. Location is now an object with `name`, `province`, and `mapLink` fields instead of a simple string.

Please update:

1. **Location search**: Search should work on both location.name and province
2. **Province filtering**: Add province-based filtering with Angola provinces:
   - Luanda, Benguela, Huambo, Huíla, Cabinda, Malanje, Uíge, Bié, Moxico, 
   - Cuando Cubango, Cunene, Namibe, Zaire, Lunda Norte, Lunda Sul, Bengo, 
   - Cuanza Norte, Cuanza Sul

3. **Search logic**: Handle both old string format and new object format
4. **Filter UI**: Add province picker/dropdown for filtering events
5. **Search suggestions**: Show location names and provinces in search suggestions
6. **API query parameters**: Update API calls to handle new location filtering

Ensure the search experience is smooth and works with both migrated and non-migrated events.
```

## 5. Update Event Creation/Editing Forms

**Prompt:**
```
Update my Expo app's event creation and editing forms to work with the new location object schema. Instead of a single location text input, the form now needs:

1. **Location Name Input**: Text input for specific location name
2. **Province Picker**: Dropdown/picker for Angola provinces with these options:
   - Luanda (default), Benguela, Huambo, Huíla, Cabinda, Malanje, Uíge, Bié, 
   - Moxico, Cuando Cubango, Cunene, Namibe, Zaire, Lunda Norte, Lunda Sul, 
   - Bengo, Cuanza Norte, Cuanza Sul

3. **Map Link Input**: Optional URL input for Google Maps link with validation

Please update:
- Form validation (required fields, URL validation for map link)
- Form submission to send location object instead of string
- Edit mode to populate fields from existing location object
- Backward compatibility when editing events with old string locations
- Error handling and user feedback
- Form state management
- Accessibility labels and hints

The form should be user-friendly and guide users to provide complete location information.
```

## 6. Update Map Integration

**Prompt:**
```
Update my Expo app's map integration to work with the new event location schema. Events now have structured location data with optional map links.

Please update:

1. **Map markers**: Use location.name for marker titles and location.province for subtitles
2. **Map links**: When location.mapLink exists, provide "Open in Maps" functionality
3. **Fallback handling**: For events without mapLink, show location name only
4. **Province grouping**: Group events by province on map views if applicable
5. **Search integration**: Allow searching by province on map screens

Map functionality to implement:
- Open external maps app when mapLink is available
- Show formatted location info in map callouts/info windows  
- Handle permission requests for location services
- Provide fallback for events with old string location format

Use React Native's Linking API for external map links and ensure smooth UX when map links are not available.
```

## 7. Update Data Persistence and Caching

**Prompt:**
```
Update my Expo app's data persistence and caching logic for the new event location schema. The location field structure has changed from string to object.

Please update:

1. **Local storage**: Update AsyncStorage/SQLite schemas if storing events locally
2. **Cache invalidation**: Update cache keys if location data affects caching
3. **Data migration**: Handle cached events with old location format
4. **Offline support**: Ensure offline functionality works with new location structure
5. **Sync logic**: Update data synchronization between local and remote data

Consider:
- Database schema changes if using local SQLite
- Cache versioning to handle schema changes
- Data transformation during app updates
- Backward compatibility with cached old-format events
- Performance implications of the new object structure

Ensure smooth app updates without data loss or crashes when the location schema changes.
```

## 8. Update Testing

**Prompt:**
```
Update my Expo app's tests to cover the new event location schema. Location has changed from string to object format with backward compatibility.

Please update:

1. **Unit tests**: Test location utility functions, type guards, and data transformations
2. **Component tests**: Test UI components with both old and new location formats
3. **Integration tests**: Test API integration with new location schema
4. **Mock data**: Update test fixtures and mock data for new location structure
5. **Edge cases**: Test handling of malformed location data, missing fields, etc.

Test scenarios to cover:
- Events with new object location format
- Events with legacy string location format  
- Events with missing/null location data
- Map link functionality (valid URLs, invalid URLs, null values)
- Province filtering and search functionality
- Form validation for location inputs
- Error handling for API responses

Ensure comprehensive test coverage for the location schema migration.
```

## Usage Instructions

1. **Choose the relevant prompts** based on what parts of your app need updating
2. **Customize the prompts** with your specific component names, file structures, and requirements
3. **Run prompts sequentially** - start with types, then API handling, then UI components
4. **Test thoroughly** after each update to ensure backward compatibility
5. **Deploy gradually** if possible to monitor for issues

## Key Considerations

- **Backward Compatibility**: Always handle both string and object location formats
- **Gradual Migration**: Your API might return mixed formats during migration
- **Error Handling**: Gracefully handle malformed or missing location data
- **User Experience**: Ensure smooth UX regardless of location data format
- **Performance**: Consider the impact of object vs string comparisons in search/filtering
