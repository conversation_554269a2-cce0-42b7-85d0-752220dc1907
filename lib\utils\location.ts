import { EventLocationType } from "@/app/(site)/event/schema";
import { getProvinceLabel } from "@/lib/constants/angola-provinces";
export const getLocationMapLink = (
  location: EventLocationType,
): string | null =>
  typeof location === "string" ? null : location.mapLink || null;
export const getFormattedLocation = (location: EventLocationType): string =>
  typeof location === "string"
    ? location
    : `${location.name}, ${getProvinceLabel(location.province)}`;

export const hasLocationMapLink = (location: EventLocationType): boolean => {
  return typeof location === "string" ? false : !!location.mapLink;
};
