import { EventType } from "@/app/(site)/event/schema";
import { getProvinceLabel } from "@/lib/constants/angola-provinces";

/**
 * Utility functions for handling event location data
 * Now works with the new dual-field approach: location (string) + locationObj (object)
 */

/**
 * Get the location name - uses the string location field for backward compatibility
 */
export const getLocationName = (event: EventType): string => {
  return event.location || "Local não informado";
};

/**
 * Get the province from locationObj, with fallback to Luanda
 */
export const getLocationProvince = (event: EventType): string => {
  return getProvinceLabel(event.locationObj?.province || "luanda");
};

/**
 * Get the Google Maps link from locationObj
 */
export const getLocationMapLink = (event: EventType): string | null => {
  return event.locationObj?.mapLink || null;
};

/**
 * Get a formatted location string for display
 * Format: "Location Name, Province"
 */
export const getFormattedLocation = (event: EventType): string => {
  const name = getLocationName(event);
  const province = getLocationProvince(event);
  return `${name}, ${province}`;
};

/**
 * Check if event has a map link available
 */
export const hasLocationMapLink = (event: EventType): boolean => {
  return !!event.locationObj?.mapLink;
};
