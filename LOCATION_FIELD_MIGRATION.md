# Event Location Field Migration

## Overview

This document outlines the migration of the Event location field from a simple string to a structured object format, while maintaining backward compatibility with existing data.

## Changes Made

### 1. Angola Provinces Constants (`lib/constants/angola-provinces.ts`)

- Created a comprehensive list of all 18 Angola provinces
- Includes value/label pairs for dropdown selection
- Set "Luanda" as the default province
- Added utility functions for province label/value conversion

### 2. Sanity Event Schema (`sanity/schemaTypes/eventType.ts`)

**Before:**
```typescript
defineField({
  name: "location",
  title: "Local",
  type: "string",
  validation: (rule) => rule.max(100).required()
})
```

**After:**
```typescript
defineField({
  name: "location",
  title: "Local",
  type: "object",
  fields: [
    {
      name: "name",
      title: "Nome do Local",
      type: "string",
      validation: (rule) => rule.max(100).required()
    },
    {
      name: "province",
      title: "Província",
      type: "string",
      options: {
        list: ANGOLA_PROVINCES.map(province => ({
          title: province.label,
          value: province.value,
        })),
        layout: "dropdown",
      },
      initialValue: DEFAULT_PROVINCE,
      validation: (rule) => rule.required()
    },
    {
      name: "mapLink",
      title: "Link do Google Maps",
      type: "url",
      description: "Link opcional para o Google Maps do local"
    }
  ]
})
```

### 3. TypeScript Event Schema (`app/(site)/event/schema.ts`)

- Added `eventLocationObjectSchema` for the new object structure
- Created `eventLocationSchema` as a union type supporting both string and object formats
- Updated main `eventSchema` to use the new location schema
- Maintained backward compatibility with existing string locations

### 4. GROQ Queries (`sanity/queries/event.ts`)

**Updated location projection in both `fullEventFields` and `eventFields`:**
```groq
"location": {
  "name": coalesce(location.name, location, "Local não informado"),
  "province": coalesce(location.province, "luanda"),
  "mapLink": location.mapLink
}
```

**Updated location filtering:**
```groq
(location.name match "${filters.location}" || location match "${filters.location}")
```

**Updated `getEventLocationsQuery`:**
```groq
*[_type == "event" && defined(location)] {
  "name": coalesce(location.name, location, "Local não informado")
} | order(name asc) | unique()
```

### 5. Category Queries (`sanity/queries/category.ts`)

- Updated location projection in category events to match the new format

### 6. Location Utilities (`lib/utils/location.ts`)

Created utility functions for handling both old and new location formats:

- `getLocationName()` - Extract location name from either format
- `getLocationProvince()` - Get province with fallback to Luanda
- `getLocationMapLink()` - Get Google Maps link (null for legacy data)
- `getFormattedLocation()` - Format as "Location Name, Province"
- `hasLocationMapLink()` - Check if map link is available

### 7. Frontend Components (`app/(site)/events/[id]/page.tsx`)

- Updated event page to use location utilities
- Added clickable map links when available
- Maintained display compatibility for legacy string locations

## Backward Compatibility

The migration ensures full backward compatibility:

1. **Existing Events**: Events with string location values continue to work
2. **GROQ Queries**: Use `coalesce()` to handle both formats
3. **TypeScript**: Union type accepts both string and object formats
4. **Frontend**: Utility functions handle both formats seamlessly
5. **API Responses**: Consistent object format returned regardless of storage format

## New Location Object Structure

```typescript
type EventLocationObject = {
  name: string;           // Required: Specific location name
  province: string;       // Required: Angola province (dropdown)
  mapLink?: string;       // Optional: Google Maps URL
}
```

## Migration Benefits

1. **Structured Data**: Better organization of location information
2. **Province Standardization**: Consistent province names across events
3. **Map Integration**: Optional Google Maps links for better UX
4. **Backward Compatibility**: No breaking changes for existing data
5. **Future Extensibility**: Easy to add more location fields

## Usage Examples

### Creating New Events
```typescript
const newEvent = {
  // ... other fields
  location: {
    name: "Centro de Convenções de Talatona",
    province: "luanda",
    mapLink: "https://maps.google.com/example"
  }
}
```

### Displaying Locations
```typescript
import { getFormattedLocation, hasLocationMapLink } from "@/lib/utils/location";

// Works with both old and new formats
const displayLocation = getFormattedLocation(event.location);
const hasMap = hasLocationMapLink(event.location);
```

## Testing

All changes have been implemented with backward compatibility in mind. Existing events with string locations will continue to work without any data migration required.
