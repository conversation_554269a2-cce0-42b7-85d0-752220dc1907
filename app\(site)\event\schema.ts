import { urlFor } from "@/sanity/lib/image";
import {
  ANGOLA_PROVINCES,
  AngolaProvinceKey,
  DEFAULT_PROVINCE,
} from "@/lib/constants/angola-provinces";
import { z } from "zod";

export const EVENT_CHECKOUT_METHODS_TYPES = {
  WHATSAPP: "WhatsApp",
  WEBSITE: "Website",
  EMAIL: "Email",
  PHONE: "Telefone",
} as const;

type E_C_M_Keys = keyof typeof EVENT_CHECKOUT_METHODS_TYPES;
export const eventCheckoutMethodsSchema = z.object({
  name: z.string(),
  type: z
    .enum(
      Object.keys(EVENT_CHECKOUT_METHODS_TYPES) as [
        E_C_M_Keys,
        ...E_C_M_Keys[],
      ],
    )
    .default("WHATSAPP"),
  value: z.string(),
});

export type EventCheckoutMethodsType = z.infer<
  typeof eventCheckoutMethodsSchema
>;

export const eventLocationObjectSchema = z.object({
  name: z
    .string()
    .min(1, "Nome do local é obrigatório")
    .max(100, "Nome do local deve ter no máximo 100 caracteres")
    .trim(),
  province: z
    .enum(
      Object.keys(ANGOLA_PROVINCES) as [
        AngolaProvinceKey,
        ...AngolaProvinceKey[],
      ],
    )
    .default(DEFAULT_PROVINCE),
  mapLink: z.url("Link deve ser uma URL válida").optional().nullable(),
});

export const eventLocationSchema = z.union([
  eventLocationObjectSchema,
  z.string().max(100, "Local deve ter no máximo 100 caracteres").trim(),
]);

export type EventLocationType = z.infer<typeof eventLocationSchema>;

export const eventActivitySchema = z
  .object({
    description: z
      .string()
      .min(5, "Descrição da atividade deve ter pelo menos 5 caracteres")
      .max(500, "Descrição da atividade deve ter no máximo 500 caracteres")
      .trim(),
    startTime: z.iso.datetime("Hora de início deve ser uma data válida"),
    endTime: z.iso
      .datetime("Hora de fim deve ser uma data válida")
      .optional()
      .nullable(),
    location: z
      .string()
      .min(5, "Local da atividade deve ter pelo menos 5 caracteres")
      .max(100, "Local da atividade deve ter no máximo 100 caracteres")
      .trim()
      .optional()
      .nullable(),
  })
  .refine(
    (data) => {
      if (!data.endTime) return true;
      const startDate = new Date(data.startTime);
      const endDate = new Date(data.endTime);
      return endDate > startDate;
    },
    {
      message: "Hora de fim deve ser posterior à hora de início",
      path: ["endTime"],
    },
  );

export type EventActivityType = z.infer<typeof eventActivitySchema>;

export const eventSchema = z.object({
  _id: z.string(),
  _type: z.literal("event"),
  _createdAt: z.iso.datetime(),
  _updatedAt: z.iso.datetime(),
  name: z.string().max(100, "Nome deve ter no máximo 100 caracteres").trim(),
  slug: z.string(),
  organizer: z
    .string()
    .max(50, "Nome do organizador deve ter no máximo 50 caracteres")
    .trim(),
  description: z
    .string()
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .trim(),
  location: z
    .string()
    .max(100, "Local deve ter no máximo 100 caracteres")
    .trim(),
  locationObj: eventLocationObjectSchema,
  startAt: z.iso.datetime(),
  endAt: z.iso.datetime(),
  highlightedUntil: z.iso.datetime().optional().nullable(),
  sponsoredUntil: z.iso.datetime().optional().nullable(),
  checkoutMethods: z.array(eventCheckoutMethodsSchema).optional().nullable(),
  prices: z
    .array(
      z.object({
        name: z.string().max(50),
        price: z.number(),
        description: z
          .string()
          .max(250, "Descrição deve ter no máximo 250 caracteres")
          .trim()
          .optional()
          .nullable(),
      }),
    )
    .default([]),
  categories: z
    .array(
      z.object({
        _id: z.string(),
        name: z.string(),
      }),
    )
    .optional()
    .nullable(),
  medias: z.array(
    z
      .object({
        _type: z.literal("image"),
        asset: z
          .object({
            _type: z.union([
              z.literal("reference"),
              z.literal("sanity.imageAsset"),
            ]),
            url: z.string().nullable(),
          })
          .nullable(),
        alt: z
          .string()
          .max(200, "Alt deve ter no máximo 200 caracteres")
          .trim()
          .optional()
          .nullable(),
        hotspot: z
          .object({
            x: z.coerce.number().optional().nullable(),
            y: z.coerce.number().optional().nullable(),
            height: z.coerce.number().optional().nullable(),
            width: z.coerce.number().optional().nullable(),
          })
          .optional()
          .nullable(),
      })
      .optional()
      .nullable()
      .transform((media) => {
        if (!media?.asset?.url) return null;
        return {
          url: urlFor(media).height(1080).fit("clip").url(),
          urlSmall: urlFor(media).width(500).height(500).fit("clip").url(),
          urlBanner: urlFor(media)
            .width(1440)
            .height(720)
            .fit("fill")
            .bg("CCCCCC")
            .url(),
        };
      }),
  ),
  activities: z.array(eventActivitySchema).optional().nullable(),
  createdBy: z
    .string()
    .max(50, "Nome do criador deve ter no máximo 50 caracteres")
    .trim(),
});

export type EventType = z.infer<typeof eventSchema>;

export const eventFiltersSchema = z.object({
  highlightedUntil: z.coerce.boolean().optional(),
  sponsoredUntil: z.coerce.boolean().optional(),
  limit: z.coerce.number().optional(),
  searchKey: z.coerce.string().optional(),
  categoryId: z.coerce.string().optional(),
  organizer: z.coerce.string().optional(),
  startAt: z.coerce.string().optional(),
  location: z.coerce.string().optional(),
  sortBy: eventSchema
    .omit({
      createdBy: true,
      medias: true,
      categories: true,
    })
    .keyof()
    .optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export type EventFiltersType = z.infer<typeof eventFiltersSchema>;
